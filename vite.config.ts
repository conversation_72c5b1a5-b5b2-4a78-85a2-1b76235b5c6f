/// <reference types="vitest" />
import path from 'node:path'
import { defineConfig, loadEnv } from 'vite'
import { createBuild } from './vitePlugin/build'
import { createVitePlugins } from './vitePlugin/plugins'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const isDev = mode === 'development'
  const env = loadEnv(mode, process.cwd())
  const dirname = import.meta.dirname

  return {
    base: isDev ? '/' : '/data',
    test: {
      globals: true,
      environment: 'jsdom',
    },
    resolve: {
      alias: {
        '@': path.resolve(dirname, 'src'),
        '#': path.resolve(dirname, 'types'),
      },
    },
    esbuild: {
      pure: ['console.log', 'console.count'],
    },
    plugins: createVitePlugins(),
    // 构建配置
    build: createBuild(),

    server: {
      host: '0.0.0.0',
      hmr: true,
      port: 8008,
      open: false,
      proxy: {
        [env.VITE_APP_BASE_API]: {
          target: 'https://whzl-customer.vankeytech.com:9905', // 测试环境
          // target: 'https://csm.scwhnk.com', // 正式环境
          changeOrigin: true,
        },
      },
    },
  }
})
