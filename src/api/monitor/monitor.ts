import type { RequestBase } from '#/index'
import type { Camera, DeviceRecord, Play, Snap, StreamInfoRequest, WVPLoginUser } from './type'
import request from '@/utils/request'

export const MonitorAPI = {
  // 监控
  monitorList(params: RequestBase) {
    return request<any, Records<Camera>>({
      url: `/customer/bigdata/device/camera/page`,
      method: 'get',
      params,
    })
  },

  // 播放
  play(deviceId: string, channelId: string) {
    return request<any, Play>({
      url: `/customer/bigdata/device/camera/play/start/${deviceId}/${channelId}`,
      method: 'get',
    })
  },

  // 云台控制
  ptzControl(deviceId: string, channelId: string, command: string) {
    return request<any, any>({
      url: `/customer/bigdata/device/camera/front-end/ptz/${deviceId}/${channelId}`,
      method: 'get',
      params: {
        command,
        horizonSpeed: 76,
        verticalSpeed: 76,
        zoomSpeed: 1,
      },
    })
  },

  // 语音广播命令
  voiceBroadcast(deviceId: string, channelId: string, broadcastMode: boolean) {
    return request<any, any>({
      url: `/customer/bigdata/device/camera/play/broadcast/${deviceId}/${channelId}`,
      method: 'get',
      // true 喊话 false 对讲
      params: { timeout: 50, broadcastMode },
    })
  },

  // 设备录像查询
  deviceRecord(deviceId: string, channelId: string, startTime: string, endTime: string) {
    return request<any, DeviceRecord>({
      url: `/customer/bigdata/device/camera/gb_record/query/${deviceId}/${channelId}`,
      method: 'get',
      params: { startTime, endTime },
    })
  },

  // 视频回放
  videoReplay(deviceId: string, channelId: string, startTime: string, endTime: string) {
    return request<any, Play>({
      url: `/customer/bigdata/device/camera/playback/start/${deviceId}/${channelId}`,
      method: 'get',
      params: { startTime, endTime },
    })
  },

  // 获取流信息
  streamInfo(params: StreamInfoRequest) {
    return request<any, any>({
      url: `/customer/bigdata/device/camera/api/server/media_server/media_info`,
      method: 'get',
    })
  },

  // 获取用户信息
  userInfo() {
    return request<any, WVPLoginUser>({
      url: `/customer/web/camera/control/user/userInfo`,
      method: 'get',
    })
  },

  // 获取苗情记录分页
  snapList(params: RequestBase) {
    return request<any, Records<Snap>>({
      url: '/customer/bigdata/deviceCameraSnapRecord/page',
      method: 'get',
      params,
    })
  },

}
