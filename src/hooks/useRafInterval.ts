import { tryOnScopeDispose } from '@vueuse/core'
import { ref } from 'vue'

/**
 * 用于替换原生setInterval
 * @param fn 函数
 * @param interval ms 执行间隔
 * @param immediate 是否立即执行
 * @return 函数 取消定时器的函数
 */
export function useRafInterval(fn: () => void, interval = 1000, immediate = true) {
  const nowTime = ref(performance.now())
  let preTime: number
  let timeDifferance = 0 // 每次的时间偏差
  let timer: number
  function refresh() {
    preTime ??= performance.now()
    if (preTime) {
      nowTime.value = performance.now()
      const realInterval = interval - timeDifferance
      if (nowTime.value - preTime >= realInterval) {
        timeDifferance = (nowTime.value - preTime - realInterval) % realInterval
        preTime = performance.now()
        fn()
      }
    }

    timer = requestAnimationFrame(refresh)
  }

  timer = requestAnimationFrame(refresh)
  // 立即执行
  immediate && fn()
  function cancelTimer() {
    cancelAnimationFrame(timer)
  }

  // 离开后销毁
  tryOnScopeDispose(() => {
    cancelAnimationFrame(timer)
  })

  return cancelTimer
}
