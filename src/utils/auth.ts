/**
 * 大数据没有单独登录系统，直接使用管理的token，所以这里的key务必保证与管理端一致
 */
const TokenKey = 'wuhuaClentAccessToken'

export function getToken(): string {
  // 是否是开发者模式
  const isDev = import.meta.env.DEV
  if (isDev) {
    // 这里的token从客户端复制，详情请查看README.md
    return 'Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJzdWIiOiJyb290Iiwibmlja25hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJjdXN0b21lcklkIjoxOCwiY2xpZW50IjoiQ1VTVE9NRVIiLCJ1c2VyVHlwZSI6IkFETUlOSVNUUkFUT1IiLCJhdXRoVHlwZSI6IlBBU1NXT1JEIiwiZXhwIjoxODE4NjM3ODg4LCJ1c2VySWQiOjEsImlhdCI6MTc1ODE1Nzg4OCwiYXV0aG9yaXRpZXMiOlsiUk9MRV9BRE1JTklTVFJBVE9SOkY5MEZFNzZBLTIxMTItNEQ5Qy1BMTU1LUMyMjRFQjc1RDE0MyIsIlJPTEVfQ1VTVE9NRVJfUk9PVCJdLCJqdGkiOiI1MWM1Y2FlZGQ4ZGQ0ZjIyYjZjZWVlMmE3MDY2NmZlMyJ9.mtd5TueQyIVtEPELiUrJk8OXA3jZ7aawfuHqsxDMyBg'
  }

  return localStorage.getItem(TokenKey)
}

export function setToken(token: string) {
  localStorage.setItem(TokenKey, token)
}

export function removeToken() {
  localStorage.removeItem(TokenKey)
}
